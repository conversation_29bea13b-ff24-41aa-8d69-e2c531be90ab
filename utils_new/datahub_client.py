# -*- coding: utf-8 -*-
"""
@Time ： 2024/12/19 
@Auth ： AI Assistant
@File ：datahub_client.py
@IDE ：PyCharm
@Motto：DataHub工具类，用于数据集管理操作
"""
import logging
from typing import List, Optional

from datahub.emitter.mce_builder import make_dataset_urn
from datahub.ingestion.graph.client import DatahubClientConfig, DataHubGraph
import datahub.emitter.mce_builder as builder
from datahub.emitter.rest_emitter import DatahubRestEmitter

# 设置日志
log = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)


class DataHubClient:
    """
    DataHub客户端工具类，提供数据集管理功能
    """
    
    def __init__(self, server_url: str = "http://120.27.143.32:8080", api_token: Optional[str] = None):
        """
        初始化DataHub客户端
        
        Args:
            server_url: DataHub服务器地址
            api_token: API认证令牌
        """
        self.server_url = server_url
        self.api_token = api_token
        self._graph = None
    
    @property
    def graph(self) -> DataHubGraph:
        """
        获取DataHubGraph实例（懒加载）
        """
        if self._graph is None:
            config = DatahubClientConfig(
                server=self.server_url,
                token=self.api_token
            )
            self._graph = DataHubGraph(config=config)
        return self._graph
    
    def delete_dataset(self, dataset_name: str, platform: str = "hive", hard: bool = False) -> bool:
        """
        删除单个数据集
        
        Args:
            dataset_name: 数据集名称
            platform: 数据平台名称，默认为"hive"
            hard: 是否硬删除，默认为False（软删除）
            
        Returns:
            bool: 删除是否成功
        """
        try:
            # 创建数据集URN
            dataset_urn = make_dataset_urn(name=dataset_name, platform=platform)
            
            # 删除数据集
            self.graph.delete_entity(urn=dataset_urn, hard=hard)
            
            delete_type = "硬删除" if hard else "软删除"
            log.info(f"{delete_type}数据集成功: {dataset_urn}")
            return True
            
        except Exception as e:
            log.error(f"删除数据集失败 {dataset_name}: {str(e)}")
            return False
    
    def delete_datasets_batch(self, dataset_names: List[str], platform: str = "hive", hard: bool = False) -> dict:
        """
        批量删除数据集
        
        Args:
            dataset_names: 数据集名称列表
            platform: 数据平台名称，默认为"hive"
            hard: 是否硬删除，默认为False（软删除）
            
        Returns:
            dict: 包含成功和失败结果的字典
        """
        results = {
            "success": [],
            "failed": [],
            "total": len(dataset_names)
        }
        
        delete_type = "硬删除" if hard else "软删除"
        log.info(f"开始批量{delete_type} {len(dataset_names)} 个数据集")
        
        for dataset_name in dataset_names:
            if self.delete_dataset(dataset_name, platform, hard):
                results["success"].append(dataset_name)
            else:
                results["failed"].append(dataset_name)
        
        log.info(f"批量删除完成: 成功 {len(results['success'])} 个, 失败 {len(results['failed'])} 个")
        
        if results["failed"]:
            log.warning(f"删除失败的数据集: {results['failed']}")
        
        return results
    
    def soft_delete_dataset(self, dataset_name: str, platform: str = "hive") -> bool:
        """
        软删除数据集（兼容性方法）
        
        Args:
            dataset_name: 数据集名称
            platform: 数据平台名称，默认为"hive"
            
        Returns:
            bool: 删除是否成功
        """
        return self.delete_dataset(dataset_name, platform, hard=False)
    
    def hard_delete_dataset(self, dataset_name: str, platform: str = "hive") -> bool:
        """
        硬删除数据集
        
        Args:
            dataset_name: 数据集名称
            platform: 数据平台名称，默认为"hive"
            
        Returns:
            bool: 删除是否成功
        """
        return self.delete_dataset(dataset_name, platform, hard=True)


def create_datahub_client_from_config(config_path: str = "./datahub_dev/config.json") -> DataHubClient:
    """
    从配置文件创建DataHub客户端
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        DataHubClient: 配置好的客户端实例
    """
    try:
        import json
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        api_token = config.get('api_token')
        server_url = config.get('server_url', 'http://120.27.143.32:8080')
        
        return DataHubClient(server_url=server_url, api_token=api_token)
        
    except Exception as e:
        log.warning(f"从配置文件加载失败: {str(e)}, 使用默认配置")
        return DataHubClient()


# 便捷函数
def quick_delete_dataset(dataset_name: str, platform: str = "hive", hard: bool = False, 
                        server_url: str = "http://120.27.143.32:8080", api_token: Optional[str] = None) -> bool:
    """
    快速删除单个数据集的便捷函数
    
    Args:
        dataset_name: 数据集名称
        platform: 数据平台名称
        hard: 是否硬删除
        server_url: DataHub服务器地址
        api_token: API认证令牌
        
    Returns:
        bool: 删除是否成功
    """
    client = DataHubClient(server_url=server_url, api_token=api_token)
    return client.delete_dataset(dataset_name, platform, hard)


def quick_delete_datasets_batch(dataset_names: List[str], platform: str = "hive", hard: bool = False,
                               server_url: str = "http://120.27.143.32:8080", api_token: Optional[str] = None) -> dict:
    """
    快速批量删除数据集的便捷函数
    
    Args:
        dataset_names: 数据集名称列表
        platform: 数据平台名称
        hard: 是否硬删除
        server_url: DataHub服务器地址
        api_token: API认证令牌
        
    Returns:
        dict: 包含成功和失败结果的字典
    """
    client = DataHubClient(server_url=server_url, api_token=api_token)
    return client.delete_datasets_batch(dataset_names, platform, hard)
