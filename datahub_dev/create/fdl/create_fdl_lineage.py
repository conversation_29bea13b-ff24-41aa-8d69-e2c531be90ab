from datahub_dev.config import api_token

import datahub.emitter.mce_builder as builder
from datahub.emitter.rest_emitter import DatahubRestEmitter
import requests
import json
import time

from utils_new.pg_client import Database

db = Database()


if __name__ == "__main__":
    # 查询FDL任务之间的关系
    results = db.execute_sql("""
                             SELECT task_id, down_task_id
                             FROM (SELECT task_id, down_task_id
                                   FROM public.test_events_task_link

                                   UNION ALL
                                   SELECT task_id, down_task_id
                                   FROM public.test_canvas_task_link) t
                             WHERE task_id IS NOT NULL
                               AND down_task_id IS NOT NULL
                             GROUP BY task_id, down_task_id
                             """)

    if results:
        for row in results:
            task_id = row[0] if row[0] else "NULL"
            down_task_id = row[1] if row[1] else "NULL"
    else:
        print("未查询到任何数据")
